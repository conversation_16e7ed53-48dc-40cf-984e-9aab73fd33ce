const ImageHelper = require('../utils/image-helper');

Page({
  data: {
    captureMode: 'photo', // 'photo' 或 'video'
    devicePosition: 'back', // 'front' 或 'back'
    recording: false,
    recordingTime: 0,
    capturedMedia: null,
    uploading: false,
    maxVideoDuration: 10, // 最大录像时长（秒）- 从服务器加载
    minVideoDuration: 0, // 最小录像时长（秒）- 从服务器加载
    recordingTimer: null,
    cameraContext: null,
    videoDurationSettings: null,
    showDebugInfo: false // 是否显示调试信息
  },

  onLoad(options) {
    // 获取传入的拍摄模式
    if (options.mode) {
      this.setData({
        captureMode: options.mode
      });
    }

    // 创建相机上下文
    this.data.cameraContext = wx.createCameraContext();

    // 检查相机权限
    this.checkCameraPermission();

    // 如果是视频模式，获取时长设置
    if (options.mode === 'video') {
      this.loadVideoDurationSettings();
    }
  },

  onUnload() {
    // 清理录制定时器
    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer);
    }

    // 获取环境信息，决定是否清理临时文件
    const envInfo = ImageHelper.getEnvironmentInfo();

    // 清理临时文件（只有在文件还存在且是本地路径时才尝试删除）
    if (this.data.capturedMedia && this.data.capturedMedia.tempFilePath) {
      const tempFilePath = this.data.capturedMedia.tempFilePath;

      // 检查是否是有效的本地临时文件路径
      if (tempFilePath &&
          !tempFilePath.startsWith('http') &&
          !tempFilePath.startsWith('/api/') &&
          (tempFilePath.includes('tmp') || tempFilePath.includes('temp'))) {

        // 在真机环境下谨慎处理文件删除，避免权限错误
        if (!envInfo.isRealDevice) {
          try {
            wx.getFileSystemManager().unlink({
              filePath: tempFilePath
            });
          } catch (error) {
            // 删除临时文件失败，忽略错误
          }
        }
      }
    }
  },

  // 加载视频时长设置
  async loadVideoDurationSettings() {
    const videoDurationManager = require('../utils/video-duration-manager');

    try {
      const settings = await videoDurationManager.loadSettings();
      this.setData({
        videoDurationSettings: settings,
        maxVideoDuration: settings.max_duration,
        minVideoDuration: settings.min_duration
      });
    } catch (error) {
      console.error('获取视频时长设置失败:', error);
      // 使用默认设置
      const defaultSettings = videoDurationManager.getSettings();
      this.setData({
        videoDurationSettings: defaultSettings,
        maxVideoDuration: defaultSettings.max_duration,
        minVideoDuration: defaultSettings.min_duration
      });
    }
  },

  // 检查相机权限
  checkCameraPermission() {
    const envInfo = ImageHelper.getEnvironmentInfo();

    // 只在真机环境下检查权限
    if (envInfo.isRealDevice) {
      wx.getSetting({
        success: (res) => {
          if (!res.authSetting['scope.camera']) {
            console.log('未获得相机权限，尝试申请');
            wx.authorize({
              scope: 'scope.camera',
              success: () => {
                console.log('相机权限申请成功');
              },
              fail: (error) => {
                console.error('相机权限申请失败:', error);
                wx.showModal({
                  title: '提示',
                  content: '需要相机权限才能使用拍照功能，请在设置中开启',
                  confirmText: '去设置',
                  success: (res) => {
                    if (res.confirm) {
                      wx.openSetting();
                    } else {
                      wx.navigateBack();
                    }
                  }
                });
              }
            });
          }
        }
      });
    }
  },

  // 相机准备就绪
  onCameraReady() {
    console.log('相机准备就绪');

    const envInfo = ImageHelper.getEnvironmentInfo();

    // 在真机环境下显示调试信息，帮助用户了解当前状态
    if (envInfo.isRealDevice) {
      this.setData({
        showDebugInfo: true
      });

      // 3秒后自动隐藏调试信息
      setTimeout(() => {
        this.setData({
          showDebugInfo: false
        });
      }, 3000);
    }
  },

  // 相机错误处理
  onCameraError(e) {

    // 根据错误类型提供更具体的提示
    let errorMessage = '相机启动失败';

    if (e.detail && e.detail.errMsg) {
      if (e.detail.errMsg.includes('auth')) {
        errorMessage = '相机权限被拒绝，请在设置中开启';
      } else if (e.detail.errMsg.includes('timeout')) {
        errorMessage = '相机启动超时，请重试';
      } else if (e.detail.errMsg.includes('device')) {
        errorMessage = '无法访问相机设备，请检查是否被其他应用占用';
      }
    }

    wx.showModal({
      title: '相机错误',
      content: errorMessage,
      confirmText: '知道了',
      showCancel: false,
      complete: () => {
        // 严重错误时返回上一页
        if (e.detail && e.detail.errMsg &&
            (e.detail.errMsg.includes('auth') || e.detail.errMsg.includes('device'))) {
          setTimeout(() => {
            wx.navigateBack();
          }, 1000);
        }
      }
    });
  },

  // 相机停止
  onCameraStop() {
    // 相机已停止
  },

  // 切换摄像头
  onSwitchCamera() {
    const newPosition = this.data.devicePosition === 'back' ? 'front' : 'back';

    console.log('切换摄像头:', {
      from: this.data.devicePosition,
      to: newPosition
    });

    this.setData({
      devicePosition: newPosition
    });
  },

  // 开始拍摄
  onCapture() {
    if (this.data.captureMode === 'photo') {
      this.takePhoto();
    } else {
      if (this.data.recording) {
        this.stopVideoRecording();
      } else {
        this.startVideoRecording();
      }
    }
  },

  // 拍照按钮按下
  onCaptureStart() {
    // 可以添加按钮按下的视觉反馈
  },

  // 拍照按钮松开
  onCaptureEnd() {
    // 可以添加按钮松开的视觉反馈
  },

  // 拍照
  takePhoto() {
    const cameraContext = this.data.cameraContext;
    if (!cameraContext) {
      wx.showToast({
        title: '相机未准备好',
        icon: 'none'
      });
      return;
    }

    const deviceInfo = wx.getDeviceInfo();
    const isRealDevice = deviceInfo.platform !== 'devtools';

    console.log('开始拍照:', {
      isRealDevice: isRealDevice,
      platform: deviceInfo.platform,
      quality: 'high'
    });

    cameraContext.takePhoto({
      quality: 'high',
      success: (res) => {
        console.log('拍照成功:', {
          tempImagePath: res.tempImagePath,
          isRealDevice: isRealDevice,
          pathLength: res.tempImagePath ? res.tempImagePath.length : 0
        });

        this.setData({
          capturedMedia: {
            type: 'photo',
            tempFilePath: res.tempImagePath
          }
        });
      },
      fail: (error) => {
        console.error('拍照失败:', {
          error: error,
          errMsg: error.errMsg,
          isRealDevice: isRealDevice
        });
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      }
    });
  },

  // 开始录像
  startVideoRecording() {
    const cameraContext = this.data.cameraContext;
    if (!cameraContext) {
      wx.showToast({
        title: '相机未准备好',
        icon: 'none'
      });
      return;
    }

    cameraContext.startRecord({
      timeoutCallback: () => {
        console.log('录制超时');
        this.stopVideoRecording();
      },
      success: () => {
        this.setData({
          recording: true,
          recordingTime: 0
        });

        // 开始计时
        this.data.recordingTimer = setInterval(() => {
          const newTime = this.data.recordingTime + 1;
          this.setData({
            recordingTime: newTime
          });

          // 达到最大时长自动停止
          if (newTime >= this.data.maxVideoDuration) {
            this.stopVideoRecording();
          }
        }, 1000);
      },
      fail: (error) => {
        console.error('开始录像失败:', error);
        wx.showToast({
          title: '录像失败',
          icon: 'none'
        });
      }
    });
  },

  // 停止录像
  stopVideoRecording() {
    const cameraContext = this.data.cameraContext;
    if (!cameraContext) return;

    // 清除计时器
    if (this.data.recordingTimer) {
      clearInterval(this.data.recordingTimer);
      this.data.recordingTimer = null;
    }

    cameraContext.stopRecord({
      success: (res) => {
        const duration = this.data.recordingTime;

        // 验证录制时长
        const videoDurationManager = require('../utils/video-duration-manager');
        const validationResult = videoDurationManager.validateDuration(duration, this.data.videoDurationSettings);

        if (!validationResult.isValid) {
          wx.showModal({
            title: '录制时长不符合要求',
            content: validationResult.message,
            showCancel: false,
            confirmText: '重新录制',
            success: () => {
              this.setData({
                recording: false,
                recordingTime: 0
              });
            }
          });
          return;
        }

        // 获取视频信息（仅在真机环境下）
        const deviceInfo = wx.getDeviceInfo();
        if (deviceInfo.platform !== 'devtools') {
          wx.getVideoInfo({
            src: res.tempVideoPath,
            success: (videoInfo) => {
              console.log('录制的视频信息:', videoInfo);
              console.log('视频尺寸:', videoInfo.width, 'x', videoInfo.height);
              console.log('视频方向:', videoInfo.width > videoInfo.height ? '横屏' : '竖屏');
            },
            fail: (error) => {
              console.warn('获取视频信息失败:', error);
            }
          });
        } else {
          console.log('开发工具环境，跳过视频信息获取');
        }

        this.setData({
          recording: false,
          capturedMedia: {
            type: 'video',
            tempFilePath: res.tempVideoPath,
            duration: duration
          }
        });
      },
      fail: (error) => {
        console.error('停止录像失败:', error);
        this.setData({
          recording: false
        });
        wx.showToast({
          title: '录像失败',
          icon: 'none'
        });
      }
    });
  },

  // 重新拍摄
  onRetake() {
    // 如果正在上传，先隐藏loading
    if (this.data.uploading) {
      wx.hideLoading();
    }

    this.setData({
      capturedMedia: null,
      recording: false,
      recordingTime: 0,
      uploading: false
    });
  },

  // 确认使用
  onConfirm() {
    if (!this.data.capturedMedia) return;

    // 不再立即上传，而是返回临时文件信息给反馈页面
    this.returnMediaToFeedbackPage();
  },

  // 返回媒体文件信息给反馈页面
  returnMediaToFeedbackPage() {
    const media = this.data.capturedMedia;
    if (!media) return;

    // 显示处理提示
    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    try {
      // 准备返回的媒体文件信息
      const mediaInfo = {
        type: media.type,
        tempFilePath: media.tempFilePath,
        // 获取文件大小信息
        size: 0 // 将在下面异步获取
      };

      // 异步获取文件信息
      wx.getFileInfo({
        filePath: media.tempFilePath,
        success: (fileInfo) => {
          mediaInfo.size = fileInfo.size;

          // 如果是视频，还需要获取时长信息
          if (media.type === 'video') {
            mediaInfo.duration = this.data.recordingTime;
          }

          wx.hideLoading();

          // 返回媒体信息给上一页
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          if (prevPage) {
            if (media.type === 'photo') {
              prevPage.onCameraPhotoResult(mediaInfo);
            } else if (media.type === 'video') {
              prevPage.onCameraVideoResult(mediaInfo);
            }
          }

          // 返回上一页
          wx.navigateBack();
        },
        fail: (error) => {
          console.warn('获取文件信息失败:', error);
          wx.hideLoading();

          // 即使获取文件信息失败，也返回基本信息
          if (media.type === 'video') {
            mediaInfo.duration = this.data.recordingTime;
          }

          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          if (prevPage) {
            if (media.type === 'photo') {
              prevPage.onCameraPhotoResult(mediaInfo);
            } else if (media.type === 'video') {
              prevPage.onCameraVideoResult(mediaInfo);
            }
          }

          // 返回上一页
          wx.navigateBack();
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('处理媒体文件失败:', error);

      wx.showModal({
        title: '处理失败',
        content: '媒体文件处理失败，请重试',
        showCancel: false
      });
    }
  },





});
